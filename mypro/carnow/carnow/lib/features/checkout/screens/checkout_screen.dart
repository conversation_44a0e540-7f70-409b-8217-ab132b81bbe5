import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/navigation/unified_app_bar.dart';
import '../../../core/theme/carnow_colors.dart';
import '../../cart/providers/cart_provider.dart';
import '../../wallet/providers/wallet_provider.dart';
import '../providers/checkout_provider.dart';
import '../widgets/shipping_address_card.dart';
import '../widgets/payment_method_card.dart';
import '../widgets/order_summary_card.dart';

/// Enhanced Checkout Screen following Forever Plan architecture
/// 
/// Features:
/// - Integration with existing wallet system
/// - Material 3 design system
/// - Real-time balance checking
/// - Proper error handling
/// - RTL support for Arabic
class CheckoutScreen extends ConsumerStatefulWidget {
  const CheckoutScreen({super.key});

  @override
  ConsumerState<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends ConsumerState<CheckoutScreen> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();
  
  String _selectedPaymentMethod = 'wallet';
  Map<String, dynamic> _shippingAddress = {};

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cartAsync = ref.watch(enhancedCartProvider);
    final walletState = ref.watch(walletProvider);
    final checkoutAsync = ref.watch(checkoutProvider);

    return Scaffold(
      appBar: const UnifiedAppBar(
        title: 'إتمام الطلب',
      ),
      body: cartAsync.when(
        data: (cart) => cart == null || cart.items.isEmpty
            ? _buildEmptyCartMessage()
            : _buildCheckoutContent(context, cart, AsyncValue.data(walletState), checkoutAsync),
        loading: () => _buildLoadingState(),
        error: (error, stackTrace) => _buildErrorState(context, error),
      ),
    );
  }

  Widget _buildEmptyCartMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'العربة فارغة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text('لا يمكن إتمام الطلب بعربة فارغة'),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('العودة للتسوق'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('جاري تحميل بيانات الطلب...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(error.toString()),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('العودة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutContent(
    BuildContext context,
    cart,
    AsyncValue walletAsync,
    AsyncValue checkoutAsync,
  ) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order Summary
                  OrderSummaryCard(cart: cart),
                  
                  const SizedBox(height: 16),
                  
                  // Shipping Address
                  ShippingAddressCard(
                    onAddressChanged: (address) {
                      setState(() {
                        _shippingAddress = address;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Payment Method
                  PaymentMethodCard(
                    selectedMethod: _selectedPaymentMethod,
                    walletBalance: walletAsync.when(
                      data: (wallet) => wallet?.balance ?? 0.0,
                      loading: () => 0.0,
                      error: (_, _) => 0.0,
                    ),
                    orderTotal: cart.total,
                    onMethodChanged: (method) {
                      setState(() {
                        _selectedPaymentMethod = method;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Order Notes
                  _buildOrderNotesSection(),
                  
                  const SizedBox(height: 100), // Space for bottom bar
                ],
              ),
            ),
          ),
          
          // Checkout Bottom Bar
          _buildCheckoutBottomBar(context, cart, checkoutAsync),
        ],
      ),
    );
  }

  Widget _buildOrderNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_outlined,
                  color: CarnowColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'ملاحظات الطلب',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _notesController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'أضف أي ملاحظات خاصة بالطلب (اختياري)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutBottomBar(
    BuildContext context,
    cart,
    AsyncValue checkoutAsync,
  ) {
    final isLoading = checkoutAsync.isLoading;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المجموع الكلي',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    cart.formattedTotal,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: CarnowColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: isLoading ? null : _placeOrder,
              icon: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.payment),
              label: Text(isLoading ? 'جاري المعالجة...' : 'تأكيد الطلب'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _placeOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_shippingAddress.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إدخال عنوان الشحن'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      final orderRequest = {
        'shipping_address': _shippingAddress,
        'payment_method': _selectedPaymentMethod,
        'notes': _notesController.text.trim(),
      };

      await ref.read(checkoutProvider.notifier).placeOrder(orderRequest);

      if (mounted) {
        // Clear cart after successful order
        ref.read(enhancedCartProvider.notifier).invalidate();
        
        // Navigate to order success screen
        Navigator.of(context).pushReplacementNamed('/order-success');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إتمام الطلب: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
