import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/navigation/unified_app_bar.dart';
import '../../../core/theme/carnow_colors.dart';
import '../providers/cart_provider.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_summary_card.dart';
import '../widgets/empty_cart_widget.dart';

/// Cart Screen following Forever Plan architecture
/// 
/// Features:
/// - Material 3 design system
/// - Real-time cart updates
/// - Proper error handling
/// - Loading states
/// - Accessibility support
/// - RTL support for Arabic
class CartScreen extends ConsumerWidget {
  const CartScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final cartAsync = ref.watch(cartProvider);
    final cartSummary = ref.watch(cartSummaryProvider);

    return Scaffold(
      appBar: UnifiedAppBar(
        title: 'عربة التسوق',
        actions: [
          if (!cartSummary.isEmpty)
            IconButton(
              onPressed: () => _showClearCartDialog(context, ref),
              icon: const Icon(Icons.delete_outline),
              tooltip: 'تفريغ العربة',
            ),
        ],
      ),
      body: cartAsync.when(
        data: (cart) => _buildCartContent(context, ref, cart),
        loading: () => _buildLoadingState(),
        error: (error, stackTrace) => _buildErrorState(context, ref, error),
      ),
      bottomNavigationBar: cartSummary.isEmpty 
          ? null 
          : _buildCheckoutBottomBar(context, ref, cartSummary),
    );
  }

  Widget _buildCartContent(BuildContext context, WidgetRef ref, cart) {
    if (cart == null || cart.isEmpty) {
      return const EmptyCartWidget();
    }

    return Column(
      children: [
        // Cart Summary Card
        CartSummaryCard(cart: cart),
        
        // Cart Items List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: cart.items.length,
            itemBuilder: (context, index) {
              final item = cart.items[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: CartItemCard(
                  item: item,
                  onQuantityChanged: (newQuantity) => 
                      _updateItemQuantity(ref, item.id, newQuantity),
                  onRemove: () => _removeItem(ref, item.id),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'جاري تحميل العربة...',
            style: TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WidgetRef ref, Object error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل العربة',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => ref.read(enhancedCartProvider.notifier).refresh(),
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCheckoutBottomBar(BuildContext context, WidgetRef ref, cartSummary) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المجموع الكلي',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    '${cartSummary.total.toStringAsFixed(3)} د.ل',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: CarnowColors.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: cartSummary.isLoading 
                  ? null 
                  : () => _proceedToCheckout(context, ref),
              icon: const Icon(Icons.shopping_cart_checkout),
              label: const Text('إتمام الطلب'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateItemQuantity(WidgetRef ref, String itemId, int quantity) async {
    try {
      await ref.read(enhancedCartProvider.notifier).updateItemQuantity(itemId, quantity);
    } catch (e) {
      // Error handling is done in the provider
    }
  }

  Future<void> _removeItem(WidgetRef ref, String itemId) async {
    try {
      await ref.read(enhancedCartProvider.notifier).removeItem(itemId);
    } catch (e) {
      // Error handling is done in the provider
    }
  }

  Future<void> _showClearCartDialog(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تفريغ العربة'),
        content: const Text('هل أنت متأكد من رغبتك في تفريغ العربة؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('تفريغ'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(enhancedCartProvider.notifier).clearCart();
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تفريغ العربة: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  void _proceedToCheckout(BuildContext context, WidgetRef ref) {
    // TODO: Navigate to checkout screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ عملية الدفع قريباً'),
      ),
    );
  }
}
